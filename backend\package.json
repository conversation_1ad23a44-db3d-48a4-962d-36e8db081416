{"name": "hrms-backend", "version": "1.0.0", "description": "HRMS Backend API with modular service architecture", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["hrms", "api", "nodejs", "express"], "author": "HRMS Team", "license": "MIT", "dependencies": {"@google/generative-ai": "^0.24.1", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "morgan": "^1.10.0", "multer": "^2.0.1", "mysql2": "^3.6.5", "pdf-parse": "^1.1.1", "uuid": "^11.1.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}